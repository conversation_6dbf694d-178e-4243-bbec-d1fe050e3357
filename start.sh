#!/bin/bash

# Set UTF-8 encoding
export LANG=en_US.UTF-8

clear

echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                🧬 DNA Encryption Platform                    ║"
echo "║                    راه‌انداز خودکار سیستم                     ║"
echo "║                                                              ║"
echo "║    ███╗   ███╗███████╗███████╗██████╗                        ║"
echo "║    ████╗ ████║██╔════╝╚══███╔╝██╔══██╗                       ║"
echo "║    ██╔████╔██║█████╗    ███╔╝ ██║  ██║                       ║"
echo "║    ██║╚██╔╝██║██╔══╝   ███╔╝  ██║  ██║                       ║"
echo "║    ██║ ╚═╝ ██║███████╗███████╗██████╔╝                       ║"
echo "║    ╚═╝     ╚═╝╚══════╝╚══════╝╚═════╝                        ║"
echo "║                                                              ║"
echo "║                    توسعه‌یافته توسط Mezd                     ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js نصب نیست. لطفاً ابتدا آن را نصب کنید."
    echo "📥 دانلود از: https://nodejs.org"
    exit 1
fi

echo "✅ Node.js شناسایی شد"
echo ""

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "❌ فایل package.json یافت نشد"
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 نصب وابستگی‌ها..."
    echo ""
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ خطا در نصب وابستگی‌ها"
        exit 1
    fi
    echo "✅ وابستگی‌ها با موفقیت نصب شدند"
else
    echo "✅ وابستگی‌ها از قبل نصب شده‌اند"
fi

echo ""
echo "🚀 راه‌اندازی سرور..."
echo ""
echo "🌐 پلتفرم در آدرس زیر در دسترس خواهد بود:"
echo "   http://localhost:8000"
echo ""
echo "💡 برای توقف سرور Ctrl+C را فشار دهید"
echo "👨‍💻 توسعه‌یافته توسط: Mezd"
echo ""

# Start the server
npm start
