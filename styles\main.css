@import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* CSS Variables for Theme System */
:root {
    /* Light Theme Colors */
    --primary-color: #6366f1;
    --primary-hover: #5855eb;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-overlay: rgba(0, 0, 0, 0.5);

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-tertiary: #94a3b8;
    --text-inverse: #ffffff;

    /* Border Colors */
    --border-primary: #e2e8f0;
    --border-secondary: #cbd5e1;
    --border-focus: var(--primary-color);

    /* Shadow Colors */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Dark Theme */
.theme-dark {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-overlay: rgba(0, 0, 0, 0.8);

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --text-inverse: #1e293b;

    --border-primary: #334155;
    --border-secondary: #475569;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Vazirmatn', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* Selection Styling */
::selection {
    background: var(--primary-color);
    color: var(--text-inverse);
}

/* Focus Styles */
:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: white;
}

.dna-loader {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
}

.dna-strand {
    position: absolute;
    width: 4px;
    height: 80px;
    background: linear-gradient(to bottom, #fff, transparent, #fff);
    border-radius: 2px;
    animation: dnaRotate 2s linear infinite;
}

.dna-strand:nth-child(1) {
    left: 20px;
    animation-delay: 0s;
}

.dna-strand:nth-child(2) {
    right: 20px;
    animation-delay: 1s;
}

@keyframes dnaRotate {
    0%, 100% { transform: rotateY(0deg) translateZ(20px); }
    50% { transform: rotateY(180deg) translateZ(20px); }
}

.loading-progress {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    margin: var(--spacing-lg) auto 0;
    overflow: hidden;
}

.loading-bar {
    height: 100%;
    background: linear-gradient(90deg, transparent, white, transparent);
    animation: loadingProgress 2s ease-in-out infinite;
}

@keyframes loadingProgress {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* App Container */
.app-container {
    display: flex;
    min-height: 100vh;
    background: var(--bg-primary);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: var(--z-fixed);
    transform: translateX(0);
    transition: transform var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.sidebar.collapsed {
    transform: translateX(100%);
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: var(--shadow-md);
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: var(--spacing-lg) 0;
    overflow-y: auto;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.nav-item.active .nav-link {
    background: linear-gradient(90deg, transparent, var(--primary-color)20);
    color: var(--primary-color);
    border-left: 3px solid var(--primary-color);
}

.nav-link i {
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.1rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.theme-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-right: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: margin-right var(--transition-normal);
}

.sidebar.collapsed + .main-content {
    margin-right: 0;
}

/* Top Header */
.top-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--spacing-lg) var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    backdrop-filter: blur(10px);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.mobile-menu-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

/* Header Right Section */
.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-stats {
    display: flex;
    gap: var(--spacing-md);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--bg-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    line-height: 1;
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.1rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.action-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.notification-badge {
    position: absolute;
    top: -2px;
    left: -2px;
    background: var(--error-color);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: var(--spacing-xl);
    overflow-y: auto;
}

/* Tab Content */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.dashboard-card .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-primary);
}

.dashboard-card .card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.dashboard-card .card-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.dashboard-card .card-content {
    padding: var(--spacing-lg);
}

/* Welcome Card */
.welcome-card .card-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.quick-actions {
    display: flex;
    gap: var(--spacing-md);
}

.quick-btn {
    flex: 1;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.quick-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.quick-btn:nth-child(2) {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.stats-grid .stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
}

.stats-grid .stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stats-grid .stat-label {
    font-size: 0.8rem;
    color: var(--text-tertiary);
}

/* Chart Controls */
.chart-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.chart-btn {
    background: none;
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.chart-btn:hover,
.chart-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Activity List */
.activity-list {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-primary);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.activity-icon.success {
    background: var(--success-color);
}

.activity-icon.warning {
    background: var(--warning-color);
}

.activity-icon.error {
    background: var(--error-color);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

/* Status List */
.status-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.status-value {
    font-weight: 500;
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
}

.status-value.online {
    background: var(--success-color);
    color: white;
}

.status-value.offline {
    background: var(--error-color);
    color: white;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: var(--spacing-sm);
}

.status-indicator.online {
    background: var(--success-color);
    box-shadow: 0 0 0 2px var(--success-color)30;
}

.status-indicator.offline {
    background: var(--error-color);
    box-shadow: 0 0 0 2px var(--error-color)30;
}

/* Encrypt Container */
.encrypt-container,
.decrypt-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: start;
}

.encrypt-card,
.decrypt-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.encrypt-card:hover,
.decrypt-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.encrypt-card .card-header,
.decrypt-card .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-primary);
}

.encrypt-card .card-header h3,
.decrypt-card .card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.encrypt-card .card-content,
.decrypt-card .card-content {
    padding: var(--spacing-lg);
}

/* File Drop Zone */
.file-drop-zone {
    border: 2px dashed var(--border-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background: var(--bg-primary);
}

.file-drop-zone:hover,
.file-drop-zone.dragover {
    border-color: var(--primary-color);
    background: var(--primary-color)05;
    transform: scale(1.02);
}

.drop-zone-content {
    position: relative;
    z-index: 2;
}

.upload-animation {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.upload-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    transition: all var(--transition-normal);
}

.file-drop-zone:hover .upload-icon {
    transform: scale(1.1);
    color: var(--primary-hover);
}

.upload-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: ripple 2s infinite;
    opacity: 0.3;
}

@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.7;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0;
    }
}

.drop-zone-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.drop-zone-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.supported-formats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.format-tag {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
}

/* File Info Modern */
.file-info-modern {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    display: none;
}

.file-info-modern.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modern Input Groups */
.input-group-modern {
    margin-bottom: var(--spacing-lg);
}

.input-group-modern label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
}

.password-input-modern,
.dna-input-modern {
    position: relative;
}

.password-input-modern input,
.dna-input-modern textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    font-family: inherit;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
    direction: rtl;
    text-align: right;
}

.password-input-modern input:focus,
.dna-input-modern textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-color)20;
}

.password-toggle-modern {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.password-toggle-modern:hover {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

.password-generator {
    position: absolute;
    left: 50px;
    top: 50%;
    transform: translateY(-50%);
}

.generate-btn {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.generate-btn:hover {
    color: var(--primary-color);
    background: var(--bg-tertiary);
}

/* Password Strength Modern */
.password-strength-modern {
    margin-top: var(--spacing-sm);
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.strength-fill {
    height: 100%;
    background: var(--error-color);
    border-radius: 2px;
    transition: all var(--transition-normal);
    width: 0%;
}

.strength-fill.weak {
    background: var(--error-color);
    width: 25%;
}

.strength-fill.fair {
    background: var(--warning-color);
    width: 50%;
}

.strength-fill.good {
    background: var(--info-color);
    width: 75%;
}

.strength-fill.strong {
    background: var(--success-color);
    width: 100%;
}

.strength-text {
    font-size: 0.8rem;
    color: var(--text-tertiary);
}

/* Password Requirements Modern */
.password-requirements-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.requirement {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
    color: var(--text-tertiary);
}

.requirement i {
    color: var(--error-color);
    font-size: 0.7rem;
}

.requirement.met i {
    color: var(--success-color);
}

.requirement.met {
    color: var(--text-secondary);
}

/* Advanced Settings */
.advanced-settings {
    margin-top: var(--spacing-lg);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.settings-header {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.settings-header h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.collapse-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.collapse-btn:hover {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

.collapse-btn.collapsed i {
    transform: rotate(-90deg);
}

.settings-content {
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    max-height: 300px;
    overflow: hidden;
    transition: max-height var(--transition-normal);
}

.settings-content.collapsed {
    max-height: 0;
    padding: 0 var(--spacing-lg);
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-primary);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info {
    flex: 1;
}

.setting-info label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
    display: block;
}

.setting-desc {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--border-secondary);
    border-radius: 24px;
    transition: all var(--transition-fast);
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: white;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.toggle-switch input:checked + label {
    background: var(--primary-color);
}

.toggle-switch input:checked + label:before {
    transform: translateX(20px);
}

/* Range Slider */
.range-slider {
    width: 120px;
}

.range-slider input[type="range"] {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: var(--bg-tertiary);
    outline: none;
    -webkit-appearance: none;
}

.range-slider input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.range-slider input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-sm);
}

.range-labels {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-xs);
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

.range-labels span:nth-child(2) {
    font-weight: 600;
    color: var(--primary-color);
}

.file-input-container {
    position: relative;
    margin-bottom: 20px;
}

.file-input-label {
    display: block;
    padding: 20px;
    border: 2px dashed #667eea;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
    color: #667eea;
    font-weight: 500;
}

.file-input-label:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

#fileInput {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    font-family: inherit;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn:active {
    transform: translateY(0);
}

.btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.btn-secondary:hover {
    box-shadow: 0 10px 25px rgba(245, 87, 108, 0.3);
}

.progress-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.progress-bar {
    width: 300px;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    color: white;
    font-size: 18px;
    font-weight: 500;
}

.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.notification {
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideIn 0.3s ease;
    border-right: 4px solid #667eea;
}

.notification-error {
    border-right-color: #e74c3c;
    background: #fdf2f2;
    color: #721c24;
}

.notification-success {
    border-right-color: #27ae60;
    background: #f0f9f4;
    color: #155724;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
    margin-right: 10px;
}

.file-info-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.file-info-card h4 {
    margin-bottom: 10px;
    color: #495057;
    font-weight: 600;
}

.file-info-card p {
    margin: 5px 0;
    color: #6c757d;
    font-weight: 400;
}

.password-strength {
    margin-top: 5px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.password-strength.weak {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.password-strength.weak::before {
    content: "ضعیف";
}

.password-strength.medium {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.password-strength.medium::before {
    content: "متوسط";
}

.password-strength.strong {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.password-strength.strong::before {
    content: "قوی";
}

.upload-section {
    grid-column: span 2;
}

.encryption-result {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.encryption-result h4 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.dna-sequence {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 15px;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
    direction: ltr;
    text-align: left;
}

.result-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-small {
    padding: 8px 16px;
    font-size: 14px;
    width: auto;
    flex: 1;
    min-width: 120px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .app-header h1 {
        font-size: 2rem;
    }
    
    section {
        padding: 20px;
    }
    
    .upload-section {
        grid-column: span 1;
    }
    
    .notification-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .progress-bar {
        width: 250px;
    }
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

/* فونت‌های فارسی برای عناصر خاص */
input, textarea, button, label {
    font-family: 'Vazirmatn', sans-serif;
}

/* تنظیمات خاص برای متن‌های انگلیسی */
.dna-sequence, code, pre {
    font-family: 'Courier New', monospace;
    direction: ltr;
    text-align: left;
}

.developer-credit {
    margin-top: 10px;
    opacity: 0.8;
    font-size: 0.9rem;
}

.app-footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: white;
    backdrop-filter: blur(10px);
}

.app-footer p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.app-footer strong {
    color: #ffd700;
    font-weight: 600;
}

/* Modern Buttons */
.btn-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    min-height: 44px;
}

.btn-modern:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-modern .btn-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.btn-modern .btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all var(--transition-fast);
}

.btn-modern.loading .btn-content {
    opacity: 0;
}

.btn-modern.loading .btn-loading {
    opacity: 1;
}

.btn-modern.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-modern.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-modern.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-modern.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-modern.btn-danger {
    background: linear-gradient(135deg, var(--error-color), #dc2626);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-modern.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Loading Spinner */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* DNA Tools Modern */
.dna-tools-modern {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    flex-wrap: wrap;
}

.tool-btn-modern {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.tool-btn-modern:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
}

/* DNA Stats */
.dna-stats {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    flex-wrap: wrap;
}

.dna-stats .stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
}

.dna-stats .stat-label {
    color: var(--text-tertiary);
    font-weight: 500;
}

.dna-stats .stat-value {
    color: var(--text-primary);
    font-weight: 600;
}

/* DNA Validation Modern */
.dna-validation-modern {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    display: none;
}

.dna-validation-modern.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

.dna-validation-modern.valid {
    background: var(--success-color)10;
    border-color: var(--success-color);
    color: var(--success-color);
}

.dna-validation-modern.invalid {
    background: var(--error-color)10;
    border-color: var(--error-color);
    color: var(--error-color);
}

/* Progress Modal */
.progress-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    backdrop-filter: blur(5px);
}

.progress-content {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 500px;
    overflow: hidden;
}

.progress-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.progress-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.progress-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.progress-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.progress-body {
    padding: var(--spacing-2xl);
    text-align: center;
}

.progress-circle {
    position: relative;
    margin: 0 auto var(--spacing-xl);
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    fill: none;
    stroke: var(--bg-tertiary);
    stroke-width: 8;
    stroke-dasharray: 339.292;
    stroke-dashoffset: 339.292;
    transition: stroke-dashoffset 0.3s ease;
}

.progress-ring-circle.active {
    stroke: var(--primary-color);
}

.progress-percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.progress-info p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-sm);
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    flex: 1;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.step.active {
    background: var(--primary-color)10;
    color: var(--primary-color);
}

.step.completed {
    background: var(--success-color)10;
    color: var(--success-color);
}

.step i {
    font-size: 1.2rem;
}

.step span {
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
}

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 500px;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* Notification System */
.notification-container-modern {
    position: fixed;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    z-index: var(--z-tooltip);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-width: 400px;
}

.notification {
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    animation: notificationSlideIn 0.3s ease-out;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
}

.notification.success::before {
    background: var(--success-color);
}

.notification.error::before {
    background: var(--error-color);
}

.notification.warning::before {
    background: var(--warning-color);
}

@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.notification.success .notification-icon {
    background: var(--success-color);
}

.notification.error .notification-icon {
    background: var(--error-color);
}

.notification.warning .notification-icon {
    background: var(--warning-color);
}

.notification.info .notification-icon {
    background: var(--info-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.9rem;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Settings Modern */
.settings-container-modern {
    max-width: 1000px;
    margin: 0 auto;
}

.settings-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
}

.settings-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.reset-settings-btn {
    background: none;
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.reset-settings-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.settings-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.settings-card .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-primary);
}

.settings-card .card-content {
    padding: var(--spacing-lg);
}

.setting-item-modern {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-primary);
}

.setting-item-modern:last-child {
    border-bottom: none;
}

.setting-item-modern .setting-info {
    flex: 1;
}

.setting-item-modern .setting-info label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
    display: block;
}

.setting-desc {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

/* Modern Select */
.modern-select {
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 120px;
}

.modern-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-color)20;
}

/* Theme Selector */
.theme-selector {
    display: flex;
    gap: var(--spacing-xs);
}

.theme-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 60px;
}

.theme-option:hover,
.theme-option.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.theme-option i {
    font-size: 1.1rem;
}

.theme-option span {
    font-size: 0.7rem;
    font-weight: 500;
}

/* Number Input Modern */
.number-input-modern {
    position: relative;
    display: flex;
    align-items: center;
}

.number-input-modern input {
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: 0.9rem;
    width: 80px;
    text-align: center;
}

.number-input-modern input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-color)20;
}

.input-unit {
    margin-left: var(--spacing-sm);
    color: var(--text-tertiary);
    font-size: 0.8rem;
    font-weight: 500;
}

/* Range Slider Modern */
.range-slider-modern {
    width: 150px;
}

.range-slider-modern input[type="range"] {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: var(--bg-tertiary);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.range-slider-modern input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.range-slider-modern input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-sm);
}

/* About Content */
.about-content {
    text-align: center;
}

.app-logo {
    margin-bottom: var(--spacing-xl);
}

.logo-icon-large {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto var(--spacing-md);
    box-shadow: var(--shadow-lg);
}

.app-logo h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.version {
    color: var(--text-tertiary);
    font-size: 0.8rem;
}

.developer-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
}

.developer-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
}

.developer-details h5 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.developer-details p {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0;
}

.system-info {
    margin-bottom: var(--spacing-xl);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-primary);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.info-value {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

.action-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

.info-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: none;
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.info-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
}

/* Analytics & History */
.analytics-container,
.history-container-modern {
    max-width: 1200px;
    margin: 0 auto;
}

.analytics-header,
.history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.analytics-header h2,
.history-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.analytics-controls,
.history-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.time-range-select {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
}

.export-btn,
.clear-history-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.export-btn:hover,
.clear-history-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.clear-history-btn {
    background: var(--error-color);
}

.clear-history-btn:hover {
    background: #dc2626;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.analytics-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.analytics-card .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    background: var(--bg-primary);
}

.analytics-card .card-content {
    padding: var(--spacing-lg);
    height: 300px;
}

.detailed-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-primary);
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-row .stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.stat-row .stat-value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.9rem;
}

/* Search Box */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    right: var(--spacing-md);
    color: var(--text-tertiary);
    font-size: 0.9rem;
}

.search-box input {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-xl) var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: 0.9rem;
    width: 250px;
    direction: rtl;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-color)20;
}

/* Filter Dropdown */
.filter-dropdown {
    position: relative;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.filter-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md);
    min-width: 150px;
    z-index: var(--z-dropdown);
    display: none;
}

.filter-menu.show {
    display: block;
    animation: fadeIn 0.2s ease-out;
}

.filter-menu label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) 0;
    cursor: pointer;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.filter-menu label:hover {
    color: var(--text-primary);
}

/* No History */
.no-history-modern {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-tertiary);
}

.no-history-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.no-history-modern h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.no-history-modern p {
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Result Section Modern */
.result-section-modern {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    display: none;
}

.result-section-modern.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        width: 260px;
    }

    .main-content {
        margin-right: 260px;
    }

    .encrypt-container,
    .decrypt-container {
        grid-template-columns: 1fr;
    }

    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: 100%;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .header-stats {
        display: none;
    }

    .page-title {
        font-size: 1.2rem;
    }

    .content-area {
        padding: var(--spacing-lg);
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .progress-steps {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .step {
        min-width: 80px;
    }

    .step span {
        font-size: 0.7rem;
    }

    .analytics-header,
    .history-header {
        flex-direction: column;
        align-items: stretch;
    }

    .analytics-controls,
    .history-controls {
        justify-content: center;
    }

    .search-box input {
        width: 200px;
    }

    .notification-container-modern {
        left: var(--spacing-sm);
        right: var(--spacing-sm);
        max-width: none;
    }

    .modal-content {
        margin: var(--spacing-lg);
        width: auto;
    }

    .progress-content {
        margin: var(--spacing-lg);
        width: auto;
    }
}

@media (max-width: 480px) {
    .content-area {
        padding: var(--spacing-md);
    }

    .dashboard-card .card-header,
    .encrypt-card .card-header,
    .decrypt-card .card-header,
    .settings-card .card-header,
    .analytics-card .card-header {
        padding: var(--spacing-md);
    }

    .dashboard-card .card-content,
    .encrypt-card .card-content,
    .decrypt-card .card-content,
    .settings-card .card-content,
    .analytics-card .card-content {
        padding: var(--spacing-md);
    }

    .btn-modern {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: 0.8rem;
    }

    .progress-body {
        padding: var(--spacing-lg);
    }

    .top-header {
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .sidebar-header,
    .sidebar-footer {
        padding: var(--spacing-md);
    }

    .nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .quick-actions {
        flex-direction: column;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dna-tools-modern {
        justify-content: center;
    }

    .dna-stats {
        justify-content: center;
    }

    .theme-selector {
        justify-content: center;
    }

    .action-buttons {
        flex-direction: column;
    }

    .analytics-controls,
    .history-controls {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .search-box input {
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .top-header,
    .progress-modal,
    .modal-overlay,
    .notification-container-modern {
        display: none !important;
    }

    .main-content {
        margin-right: 0 !important;
    }

    .content-area {
        padding: 0 !important;
    }

    .dashboard-card,
    .encrypt-card,
    .decrypt-card,
    .settings-card,
    .analytics-card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
        break-inside: avoid;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --border-primary: #000000;
        --border-secondary: #333333;
        --text-primary: #000000;
        --text-secondary: #333333;
    }

    .theme-dark {
        --border-primary: #ffffff;
        --border-secondary: #cccccc;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .loading-screen,
    .progress-modal,
    .modal-overlay {
        animation: none !important;
    }
}

/* Focus Visible */
@supports selector(:focus-visible) {
    :focus {
        outline: none;
    }

    :focus-visible {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
}


