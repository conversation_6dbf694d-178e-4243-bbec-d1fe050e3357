<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNA Encryption Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        .success { color: #4ade80; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5b5bf6;
        }
        #results {
            margin-top: 20px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧬 DNA Encryption Platform - Test Page</h1>
    
    <div class="test-section">
        <h2>Module Loading Test</h2>
        <button onclick="testModuleLoading()">Test Module Loading</button>
        <div id="moduleResults"></div>
    </div>

    <div class="test-section">
        <h2>Theme & Language Test</h2>
        <button onclick="testThemeSwitch()">Test Theme Switch</button>
        <button onclick="testLanguageSwitch()">Test Language Switch</button>
        <div id="themeResults"></div>
    </div>

    <div class="test-section">
        <h2>DNA Encoding Test</h2>
        <button onclick="testDNAEncoding()">Test DNA Encoding</button>
        <div id="dnaResults"></div>
    </div>

    <div class="test-section">
        <h2>UI Components Test</h2>
        <button onclick="testNotifications()">Test Notifications</button>
        <button onclick="testProgressModal()">Test Progress Modal</button>
        <div id="uiResults"></div>
    </div>

    <div id="results"></div>

    <!-- Load the same scripts as main app -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    
    <!-- Load core modules -->
    <script src="src/core/DNACodec.js"></script>
    <script src="src/core/AdvancedCryptoEngine.js"></script>
    <script src="src/core/AdvancedFileHandler.js"></script>
    <script src="src/storage/StorageManager.js"></script>
    <script src="src/ui/UIManager.js"></script>
    <script src="src/DNAEncryptionApp.js"></script>

    <script>
        let testResults = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateResults();
        }

        function updateResults() {
            document.getElementById('results').textContent = testResults.join('\n');
        }

        function testModuleLoading() {
            const moduleResults = document.getElementById('moduleResults');
            const requiredClasses = ['DNACodec', 'AdvancedCryptoEngine', 'AdvancedFileHandler', 'StorageManager', 'UIManager', 'DNAEncryptionApp'];
            let results = [];

            requiredClasses.forEach(className => {
                if (typeof window[className] !== 'undefined') {
                    results.push(`<span class="success">✓ ${className} loaded</span>`);
                    log(`${className} loaded successfully`, 'success');
                } else {
                    results.push(`<span class="error">✗ ${className} missing</span>`);
                    log(`${className} failed to load`, 'error');
                }
            });

            moduleResults.innerHTML = results.join('<br>');
        }

        function testThemeSwitch() {
            try {
                const uiManager = new UIManager();
                uiManager.setTheme('light');
                log('Theme switched to light', 'success');
                
                setTimeout(() => {
                    uiManager.setTheme('dark');
                    log('Theme switched back to dark', 'success');
                }, 1000);
                
                document.getElementById('themeResults').innerHTML = '<span class="success">✓ Theme switching works</span>';
            } catch (error) {
                log(`Theme switch error: ${error.message}`, 'error');
                document.getElementById('themeResults').innerHTML = '<span class="error">✗ Theme switching failed</span>';
            }
        }

        function testLanguageSwitch() {
            try {
                const uiManager = new UIManager();
                uiManager.setLanguage('en');
                log('Language switched to English', 'success');
                
                setTimeout(() => {
                    uiManager.setLanguage('fa');
                    log('Language switched back to Persian', 'success');
                }, 1000);
                
                document.getElementById('themeResults').innerHTML += '<br><span class="success">✓ Language switching works</span>';
            } catch (error) {
                log(`Language switch error: ${error.message}`, 'error');
                document.getElementById('themeResults').innerHTML += '<br><span class="error">✗ Language switching failed</span>';
            }
        }

        function testDNAEncoding() {
            try {
                const codec = new DNACodec();
                const testData = new Uint8Array([72, 101, 108, 108, 111]); // "Hello"
                const encoded = codec.encodeWithErrorCorrection(testData);
                log(`DNA encoding successful: ${encoded.substring(0, 50)}...`, 'success');
                
                const decoded = codec.decodeWithErrorCorrection(encoded);
                const decodedText = String.fromCharCode(...decoded);
                log(`DNA decoding successful: ${decodedText}`, 'success');
                
                document.getElementById('dnaResults').innerHTML = '<span class="success">✓ DNA encoding/decoding works</span>';
            } catch (error) {
                log(`DNA encoding error: ${error.message}`, 'error');
                document.getElementById('dnaResults').innerHTML = '<span class="error">✗ DNA encoding failed</span>';
            }
        }

        function testNotifications() {
            try {
                const uiManager = new UIManager();
                uiManager.showNotification('Test notification', 'success', 'Test');
                log('Notification test successful', 'success');
                document.getElementById('uiResults').innerHTML = '<span class="success">✓ Notifications work</span>';
            } catch (error) {
                log(`Notification error: ${error.message}`, 'error');
                document.getElementById('uiResults').innerHTML = '<span class="error">✗ Notifications failed</span>';
            }
        }

        function testProgressModal() {
            try {
                const uiManager = new UIManager();
                // This would need DOM elements to work properly
                log('Progress modal test - requires full DOM', 'warning');
                document.getElementById('uiResults').innerHTML += '<br><span class="warning">⚠ Progress modal needs full DOM</span>';
            } catch (error) {
                log(`Progress modal error: ${error.message}`, 'error');
                document.getElementById('uiResults').innerHTML += '<br><span class="error">✗ Progress modal failed</span>';
            }
        }

        // Auto-run basic tests
        window.addEventListener('load', () => {
            log('Test page loaded', 'info');
            setTimeout(() => {
                testModuleLoading();
            }, 500);
        });
    </script>
</body>
</html>
