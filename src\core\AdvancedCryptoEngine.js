class AdvancedCryptoEngine {
    constructor() {
        this.keyDerivationIterations = 100000;
        this.saltLength = 32;
        this.ivLength = 16;
    }

    // PBKDF2 Key Derivation
    async deriveKey(password, salt) {
        const encoder = new TextEncoder();
        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            encoder.encode(password),
            { name: 'PBKDF2' },
            false,
            ['deriveBits', 'deriveKey']
        );

        return crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: this.keyDerivationIterations,
                hash: 'SHA-256'
            },
            keyMaterial,
            { name: 'AES-GCM', length: 256 },
            false,
            ['encrypt', 'decrypt']
        );
    }

    // AES-GCM Encryption before DNA encoding
    async encryptBeforeDNA(data, password) {
        const salt = crypto.getRandomValues(new Uint8Array(this.saltLength));
        const iv = crypto.getRandomValues(new Uint8Array(this.ivLength));
        const key = await this.deriveKey(password, salt);

        const encrypted = await crypto.subtle.encrypt(
            { name: 'AES-GCM', iv: iv },
            key,
            data
        );

        // Combine salt + iv + encrypted data
        const result = new Uint8Array(salt.length + iv.length + encrypted.byteLength);
        result.set(salt, 0);
        result.set(iv, salt.length);
        result.set(new Uint8Array(encrypted), salt.length + iv.length);
        
        return result;
    }

    // AES-GCM Decryption after DNA decoding
    async decryptAfterDNA(encryptedData, password) {
        const salt = encryptedData.slice(0, this.saltLength);
        const iv = encryptedData.slice(this.saltLength, this.saltLength + this.ivLength);
        const encrypted = encryptedData.slice(this.saltLength + this.ivLength);

        const key = await this.deriveKey(password, salt);

        try {
            const decrypted = await crypto.subtle.decrypt(
                { name: 'AES-GCM', iv: iv },
                key,
                encrypted
            );
            return new Uint8Array(decrypted);
        } catch (error) {
            throw new Error('Decryption failed: Invalid password or corrupted data');
        }
    }

    // Generate secure random password
    generateSecurePassword(length = 32) {
        const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
        const randomValues = crypto.getRandomValues(new Uint8Array(length));
        return Array.from(randomValues, byte => charset[byte % charset.length]).join('');
    }

    // Hash function for integrity checking
    async hashData(data) {
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return new Uint8Array(hashBuffer);
    }
}

// Ensure global availability
window.AdvancedCryptoEngine = AdvancedCryptoEngine;