class AdvancedFileHandler {
    constructor() {
        this.supportedFormats = [
            'text/*', 'image/*', 'audio/*', 'video/*', 
            'application/pdf', 'application/msword',
            'application/zip', 'application/json'
        ];
        this.maxFileSize = 500 * 1024 * 1024; // 500MB
        this.chunkSize = 64 * 1024; // 64KB chunks
    }

    // Chunked Processing for Large Files
    async processLargeFile(file, progressCallback) {
        const chunks = Math.ceil(file.size / this.chunkSize);
        let processedData = new Uint8Array(file.size);
        let offset = 0;

        for (let i = 0; i < chunks; i++) {
            const chunk = file.slice(offset, offset + this.chunkSize);
            const chunkData = new Uint8Array(await chunk.arrayBuffer());
            processedData.set(chunkData, offset);
            
            offset += this.chunkSize;
            progressCallback((i + 1) / chunks * 100);
            
            // Yield control to prevent UI freezing
            await new Promise(resolve => setTimeout(resolve, 0));
        }
        
        return processedData;
    }

    // Advanced MIME Detection
    detectFileType(arrayBuffer) {
        const bytes = new Uint8Array(arrayBuffer);
        const signatures = {
            'image/jpeg': [0xFF, 0xD8, 0xFF],
            'image/png': [0x89, 0x50, 0x4E, 0x47],
            'application/pdf': [0x25, 0x50, 0x44, 0x46],
            'video/mp4': [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70],
            'application/zip': [0x50, 0x4B, 0x03, 0x04],
            'text/plain': [0x54, 0x68, 0x69, 0x73],
            'audio/mp3': [0x49, 0x44, 0x33]
        };

        for (const [mimeType, signature] of Object.entries(signatures)) {
            if (this.matchesSignature(bytes, signature)) {
                return mimeType;
            }
        }
        return 'application/octet-stream';
    }

    matchesSignature(bytes, signature) {
        return signature.every((byte, index) => bytes[index] === byte);
    }

    // File validation
    validateFile(file) {
        if (!file) {
            throw new Error('No file selected');
        }
        
        if (file.size > this.maxFileSize) {
            throw new Error(`File size exceeds maximum limit of ${this.maxFileSize / (1024 * 1024)}MB`);
        }

        const isSupported = this.supportedFormats.some(format => {
            if (format.endsWith('/*')) {
                return file.type.startsWith(format.slice(0, -1));
            }
            return file.type === format;
        });

        if (!isSupported) {
            console.warn(`File type ${file.type} may not be fully supported`);
        }

        return true;
    }

    // Get file metadata
    getFileMetadata(file) {
        return {
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: file.lastModified,
            lastModifiedDate: new Date(file.lastModified)
        };
    }
}

// Ensure global availability
window.AdvancedFileHandler = AdvancedFileHandler;