class UIManager {
    constructor() {
        this.progressModal = null;
        this.progressRing = null;
        this.progressPercentage = null;
        this.progressText = null;
        this.progressSteps = null;
        this.notificationContainer = null;
        this.currentTab = 'dashboard';
        this.sidebar = null;
        this.themeToggle = null;
        this.currentTheme = 'dark';
        this.notifications = [];
        this.charts = {};

        this.messages = {
            processing: 'در حال پردازش...',
            encrypting: 'در حال رمزگذاری...',
            decrypting: 'در حال رمزگشایی...',
            compressing: 'در حال فشرده‌سازی...',
            decompressing: 'در حال باز کردن فشرده‌سازی...',
            encoding: 'در حال تبدیل به DNA...',
            decoding: 'در حال تبدیل از DNA...',
            complete: 'تکمیل شد',
            error: 'خطا',
            success: 'موفق',
            fileSelected: 'فایل انتخاب شد',
            encryptionComplete: 'رمزگذاری با موفقیت انجام شد!',
            decryptionComplete: 'رمزگشایی با موفقیت انجام شد! دانلود آغاز شد.',
            downloadStarted: 'دانلود آغاز شد',
            loadingSystem: 'بارگذاری سیستم...',
            initializingComponents: 'راه‌اندازی کامپوننت‌ها...',
            preparingInterface: 'آماده‌سازی رابط کاربری...',
            systemReady: 'سیستم آماده است'
        };

        // Animation settings
        this.animationEnabled = true;
        this.soundEnabled = true;

        // Keyboard shortcuts
        this.shortcuts = {
            'ctrl+o': () => this.triggerFileSelect(),
            'ctrl+e': () => this.switchTab('encrypt'),
            'ctrl+d': () => this.switchTab('decrypt'),
            'ctrl+h': () => this.switchTab('history'),
            'ctrl+,': () => this.switchTab('settings'),
            'f11': () => this.toggleFullscreen(),
            'ctrl+?': () => this.showShortcutsModal(),
            'escape': () => this.closeModals()
        };
    }

    async initialize() {
        // Show loading screen
        this.showLoadingScreen();

        // Initialize DOM elements
        this.progressModal = document.getElementById('progressModal');
        this.progressRing = document.querySelector('.progress-ring-circle');
        this.progressPercentage = document.getElementById('progressPercentage');
        this.progressText = document.getElementById('progressText');
        this.progressSteps = document.querySelectorAll('.step');
        this.notificationContainer = document.getElementById('notificationContainer');
        this.sidebar = document.getElementById('sidebar');
        this.themeToggle = document.getElementById('themeToggle');

        // Setup event listeners
        this.setupEventListeners();

        // Initialize theme
        this.initializeTheme();

        // Initialize charts
        await this.initializeCharts();

        // Setup keyboard shortcuts
        this.setupKeyboardShortcuts();

        // Setup animations
        this.setupAnimations();

        // Hide loading screen
        setTimeout(() => {
            this.hideLoadingScreen();
            this.showNotification('سیستم با موفقیت بارگذاری شد', 'success');
        }, 2000);
    }

    showLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');

            // Animate loading progress
            const loadingBar = document.querySelector('.loading-bar');
            if (loadingBar) {
                setTimeout(() => {
                    loadingBar.style.width = '100%';
                }, 500);
            }
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.classList.add('hidden');
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }

    setupEventListeners() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Navigation
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = item.dataset.tab;
                if (tab) {
                    this.switchTab(tab);
                }
            });
        });

        // Theme toggle
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Theme selector buttons
        const themeOptions = document.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
            option.addEventListener('click', () => {
                const theme = option.dataset.theme;
                this.setTheme(theme);
            });
        });

        // Fullscreen toggle
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        }

        // Modal close buttons
        const modalCloses = document.querySelectorAll('.modal-close, .progress-close');
        modalCloses.forEach(btn => {
            btn.addEventListener('click', () => this.closeModals());
        });

        // Advanced settings toggle
        const advancedToggle = document.getElementById('advancedToggle');
        if (advancedToggle) {
            advancedToggle.addEventListener('click', () => this.toggleAdvancedSettings());
        }

        // Password generator
        const generatePassword = document.getElementById('generatePassword');
        if (generatePassword) {
            generatePassword.addEventListener('click', () => this.generateSecurePassword());
        }

        // DNA tools
        this.setupDNATools();

        // File drop zone
        this.setupFileDropZone();

        // Context menu
        this.setupContextMenu();

        // Window resize
        window.addEventListener('resize', () => this.handleResize());

        // Escape key for modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModals();
            }
        });
    }

    initializeTheme() {
        // Load saved theme or use default
        const savedTheme = localStorage.getItem('dna-app-theme') || 'dark';
        this.setTheme(savedTheme);
    }

    setTheme(theme) {
        this.currentTheme = theme;
        document.body.className = `theme-${theme}`;
        localStorage.setItem('dna-app-theme', theme);

        // Update theme selector
        const themeOptions = document.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
            option.classList.toggle('active', option.dataset.theme === theme);
        });

        // Update theme toggle icon
        if (this.themeToggle) {
            const icon = this.themeToggle.querySelector('i');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }

    async initializeCharts() {
        // Initialize activity chart
        const activityCtx = document.getElementById('activityChart');
        if (activityCtx && typeof Chart !== 'undefined') {
            this.charts.activity = new Chart(activityCtx, {
                type: 'line',
                data: {
                    labels: ['شنبه', 'یکشنبه', 'دوشنبه', 'سه‌شنبه', 'چهارشنبه', 'پنج‌شنبه', 'جمعه'],
                    datasets: [{
                        label: 'رمزگذاری',
                        data: [0, 0, 0, 0, 0, 0, 0],
                        borderColor: '#6366f1',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // Initialize performance chart
        const performanceCtx = document.getElementById('performanceChart');
        if (performanceCtx && typeof Chart !== 'undefined') {
            this.charts.performance = new Chart(performanceCtx, {
                type: 'bar',
                data: {
                    labels: ['رمزگذاری', 'رمزگشایی', 'فشرده‌سازی', 'تصحیح خطا'],
                    datasets: [{
                        label: 'زمان (میلی‌ثانیه)',
                        data: [0, 0, 0, 0],
                        backgroundColor: [
                            'rgba(99, 102, 241, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(6, 182, 212, 0.8)',
                            'rgba(16, 185, 129, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        // Initialize file type chart
        const fileTypeCtx = document.getElementById('fileTypeChart');
        if (fileTypeCtx && typeof Chart !== 'undefined') {
            this.charts.fileType = new Chart(fileTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['PDF', 'تصاویر', 'ویدیو', 'متن', 'سایر'],
                    datasets: [{
                        data: [0, 0, 0, 0, 0],
                        backgroundColor: [
                            '#ef4444',
                            '#f59e0b',
                            '#10b981',
                            '#3b82f6',
                            '#8b5cf6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            const key = [];
            if (e.ctrlKey) key.push('ctrl');
            if (e.altKey) key.push('alt');
            if (e.shiftKey) key.push('shift');
            key.push(e.key.toLowerCase());

            const shortcut = key.join('+');
            if (this.shortcuts[shortcut]) {
                e.preventDefault();
                this.shortcuts[shortcut]();
            }
        });
    }

    setupAnimations() {
        if (typeof gsap !== 'undefined' && this.animationEnabled) {
            // Animate dashboard cards on load
            gsap.from('.dashboard-card', {
                duration: 0.6,
                y: 30,
                opacity: 0,
                stagger: 0.1,
                ease: 'power2.out'
            });

            // Animate sidebar navigation
            gsap.from('.nav-item', {
                duration: 0.4,
                x: -20,
                opacity: 0,
                stagger: 0.05,
                delay: 0.2,
                ease: 'power2.out'
            });
        }
    }

    showProgress(text, progress = 0, step = 1) {
        if (this.progressModal) {
            this.progressModal.style.display = 'flex';
            this.updateProgress(progress, text, step);
        }
    }

    updateProgress(progress, text = null, step = null) {
        if (this.progressPercentage) {
            this.progressPercentage.textContent = `${Math.round(progress)}%`;
        }

        if (this.progressRing) {
            const circumference = 2 * Math.PI * 54;
            const offset = circumference - (progress / 100) * circumference;
            this.progressRing.style.strokeDashoffset = offset;

            if (progress > 0) {
                this.progressRing.classList.add('active');
            }
        }

        if (text && this.progressText) {
            this.progressText.textContent = text;
        }

        if (step && this.progressSteps) {
            this.progressSteps.forEach((stepEl, index) => {
                stepEl.classList.remove('active', 'completed');
                if (index + 1 < step) {
                    stepEl.classList.add('completed');
                } else if (index + 1 === step) {
                    stepEl.classList.add('active');
                }
            });
        }
    }

    hideProgress() {
        if (this.progressModal) {
            this.progressModal.style.display = 'none';
        }

        // Reset progress ring
        if (this.progressRing) {
            this.progressRing.classList.remove('active');
            this.progressRing.style.strokeDashoffset = 339.292;
        }

        // Reset steps
        if (this.progressSteps) {
            this.progressSteps.forEach(step => {
                step.classList.remove('active', 'completed');
            });
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;
        
        this.notificationContainer.appendChild(notification);
        
        // حذف خودکار بعد از ۵ ثانیه
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    showError(message) {
        this.showNotification(`❌ ${message}`, 'error');
    }

    showSuccess(message) {
        this.showNotification(`✅ ${message}`, 'success');
    }

    displayEncryptionResult(dnaSequence, encryptionId) {
        const resultContainer = document.getElementById('encryptionResult');
        if (!resultContainer) return;

        const compressionRatio = this.calculateCompressionRatio(dnaSequence);
        
        resultContainer.innerHTML = `
            <div class="encryption-result">
                <h4>🧬 نتیجه رمزگذاری</h4>
                <div class="result-stats">
                    <p><strong>طول توالی DNA:</strong> ${dnaSequence.length.toLocaleString('fa-IR')} نوکلئوتید</p>
                    <p><strong>نرخ فشرده‌سازی:</strong> ${compressionRatio}%</p>
                    <p><strong>شناسه رمزگذاری:</strong> ${encryptionId}</p>
                </div>
                <div class="dna-sequence">${dnaSequence}</div>
                <div class="result-actions">
                    <button onclick="navigator.clipboard.writeText('${dnaSequence}')" class="btn btn-small">
                        📋 کپی توالی DNA
                    </button>
                    <button onclick="window.uiManager.downloadDNA('${dnaSequence}', '${encryptionId}')" class="btn btn-small">
                        💾 دانلود فایل DNA
                    </button>
                    <button onclick="window.uiManager.generateQRCode('${dnaSequence}')" class="btn btn-small">
                        📱 تولید کد QR
                    </button>
                </div>
            </div>
        `;
    }

    calculateCompressionRatio(dnaSequence) {
        // محاسبه ساده نرخ فشرده‌سازی
        return Math.round((dnaSequence.length / (dnaSequence.length * 2)) * 100);
    }

    downloadDNA(dnaSequence, encryptionId) {
        const blob = new Blob([dnaSequence], { type: 'text/plain; charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `dna_encrypted_${encryptionId}.dna`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess('فایل DNA دانلود شد');
    }

    offerDownload(data, filename) {
        const blob = new Blob([data]);
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess(this.messages.decryptionComplete);
    }

    generateQRCode(dnaSequence) {
        // جای‌گذار برای تولید کد QR
        this.showNotification('قابلیت تولید کد QR به زودی اضافه خواهد شد!', 'info');
    }

    displayFileInfo(metadata) {
        const fileInfo = document.getElementById('fileInfo');
        if (!fileInfo) return;

        fileInfo.innerHTML = `
            <div class="file-info-card">
                <h4>📄 اطلاعات فایل</h4>
                <p><strong>نام:</strong> ${metadata.name}</p>
                <p><strong>اندازه:</strong> ${this.formatFileSize(metadata.size)}</p>
                <p><strong>نوع:</strong> ${metadata.type}</p>
                <p><strong>تاریخ تغییر:</strong> ${new Date(metadata.lastModified).toLocaleDateString('fa-IR')}</p>
            </div>
        `;
    }

    formatFileSize(bytes) {
        const sizes = ['بایت', 'کیلوبایت', 'مگابایت', 'گیگابایت'];
        if (bytes === 0) return '0 بایت';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        const size = Math.round(bytes / Math.pow(1024, i) * 100) / 100;
        return `${size.toLocaleString('fa-IR')} ${sizes[i]}`;
    }

    // Navigation methods
    toggleSidebar() {
        if (this.sidebar) {
            this.sidebar.classList.toggle('show');
            this.sidebar.classList.toggle('collapsed');
        }
    }

    switchTab(tabName) {
        // Update navigation
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.classList.toggle('active', item.dataset.tab === tabName);
        });

        // Update content
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-tab`);
        });

        this.currentTab = tabName;

        // Animate tab transition
        if (typeof gsap !== 'undefined' && this.animationEnabled) {
            const activeTab = document.querySelector('.tab-content.active');
            if (activeTab) {
                gsap.from(activeTab, {
                    duration: 0.3,
                    opacity: 0,
                    y: 20,
                    ease: 'power2.out'
                });
            }
        }

        // Close sidebar on mobile after navigation
        if (window.innerWidth <= 768) {
            this.sidebar?.classList.remove('show');
        }
    }

    // Utility methods
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    triggerFileSelect() {
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.click();
        }
    }

    closeModals() {
        const modals = document.querySelectorAll('.modal-overlay, .progress-modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
    }

    showShortcutsModal() {
        const modal = document.getElementById('shortcutsModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    toggleAdvancedSettings() {
        const settings = document.getElementById('advancedSettings');
        const toggle = document.getElementById('advancedToggle');

        if (settings && toggle) {
            settings.classList.toggle('collapsed');
            toggle.classList.toggle('collapsed');
        }
    }

    generateSecurePassword() {
        const length = 16;
        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        let password = '';

        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }

        const passwordInput = document.getElementById('encryptPassword');
        if (passwordInput) {
            passwordInput.value = password;
            passwordInput.dispatchEvent(new Event('input'));
        }

        this.showNotification('رمز عبور امن تولید شد', 'success');
    }

    setupDNATools() {
        // DNA validation and tools
        const dnaInput = document.getElementById('dnaInput');
        if (dnaInput) {
            dnaInput.addEventListener('input', () => this.updateDNAStats());
        }

        // DNA tool buttons
        const pasteBtn = document.getElementById('pasteBtn');
        const clearBtn = document.getElementById('clearBtn');
        const validateBtn = document.getElementById('validateBtn');
        const formatBtn = document.getElementById('formatBtn');

        if (pasteBtn) {
            pasteBtn.addEventListener('click', () => this.pasteDNA());
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearDNA());
        }

        if (validateBtn) {
            validateBtn.addEventListener('click', () => this.validateDNA());
        }

        if (formatBtn) {
            formatBtn.addEventListener('click', () => this.formatDNA());
        }
    }

    updateDNAStats() {
        const dnaInput = document.getElementById('dnaInput');
        if (!dnaInput) return;

        const sequence = dnaInput.value.toUpperCase().replace(/[^ATCG]/g, '');
        const stats = {
            length: sequence.length,
            A: (sequence.match(/A/g) || []).length,
            T: (sequence.match(/T/g) || []).length,
            C: (sequence.match(/C/g) || []).length,
            G: (sequence.match(/G/g) || []).length
        };

        // Update stats display
        const lengthEl = document.getElementById('dnaLength');
        const countA = document.getElementById('countA');
        const countT = document.getElementById('countT');
        const countC = document.getElementById('countC');
        const countG = document.getElementById('countG');

        if (lengthEl) lengthEl.textContent = stats.length.toLocaleString('fa-IR');
        if (countA) countA.textContent = stats.A.toLocaleString('fa-IR');
        if (countT) countT.textContent = stats.T.toLocaleString('fa-IR');
        if (countC) countC.textContent = stats.C.toLocaleString('fa-IR');
        if (countG) countG.textContent = stats.G.toLocaleString('fa-IR');
    }

    async pasteDNA() {
        try {
            const text = await navigator.clipboard.readText();
            const dnaInput = document.getElementById('dnaInput');
            if (dnaInput) {
                dnaInput.value = text;
                this.updateDNAStats();
                this.showNotification('متن از کلیپ‌بورد چسبانده شد', 'success');
            }
        } catch (err) {
            this.showNotification('خطا در دسترسی به کلیپ‌بورد', 'error');
        }
    }

    clearDNA() {
        const dnaInput = document.getElementById('dnaInput');
        if (dnaInput) {
            dnaInput.value = '';
            this.updateDNAStats();
            this.showNotification('متن پاک شد', 'info');
        }
    }

    validateDNA() {
        const dnaInput = document.getElementById('dnaInput');
        const validation = document.getElementById('dnaValidation');

        if (!dnaInput || !validation) return;

        const sequence = dnaInput.value.toUpperCase();
        const validChars = /^[ATCG\s\n\r]*$/;
        const isValid = validChars.test(sequence) && sequence.trim().length > 0;

        validation.className = `dna-validation-modern show ${isValid ? 'valid' : 'invalid'}`;
        validation.textContent = isValid ?
            '✅ توالی DNA معتبر است' :
            '❌ توالی DNA نامعتبر - فقط حروف A, T, C, G مجاز هستند';
    }

    formatDNA() {
        const dnaInput = document.getElementById('dnaInput');
        if (!dnaInput) return;

        let sequence = dnaInput.value.toUpperCase().replace(/[^ATCG]/g, '');

        // Format in groups of 10 with line breaks every 50
        let formatted = '';
        for (let i = 0; i < sequence.length; i += 50) {
            const line = sequence.substr(i, 50);
            const groups = line.match(/.{1,10}/g) || [];
            formatted += groups.join(' ') + '\n';
        }

        dnaInput.value = formatted.trim();
        this.updateDNAStats();
        this.showNotification('توالی DNA فرمت شد', 'success');
    }

    setupFileDropZone() {
        const dropZone = document.getElementById('fileDropZone');
        if (!dropZone) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.add('dragover');
            });
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.remove('dragover');
            });
        });

        dropZone.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const fileInput = document.getElementById('fileInput');
                if (fileInput) {
                    fileInput.files = files;
                    fileInput.dispatchEvent(new Event('change'));
                }
            }
        });
    }

    setupContextMenu() {
        const contextMenu = document.getElementById('contextMenu');
        if (!contextMenu) return;

        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            contextMenu.style.display = 'block';
            contextMenu.style.left = `${e.pageX}px`;
            contextMenu.style.top = `${e.pageY}px`;
        });

        document.addEventListener('click', () => {
            contextMenu.style.display = 'none';
        });

        // Context menu actions
        const menuItems = contextMenu.querySelectorAll('.context-menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const action = item.dataset.action;
                this.handleContextAction(action);
                contextMenu.style.display = 'none';
            });
        });
    }

    handleContextAction(action) {
        switch (action) {
            case 'copy':
                document.execCommand('copy');
                this.showNotification('کپی شد', 'success');
                break;
            case 'paste':
                document.execCommand('paste');
                this.showNotification('چسبانده شد', 'success');
                break;
            case 'select-all':
                document.execCommand('selectAll');
                break;
            case 'download':
                // Handle download action
                break;
        }
    }

    handleResize() {
        // Handle responsive behavior
        if (window.innerWidth <= 768) {
            this.sidebar?.classList.add('collapsed');
        } else {
            this.sidebar?.classList.remove('collapsed');
        }

        // Resize charts
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.resize) {
                chart.resize();
            }
        });
    }
}

// دسترسی سراسری به UIManager
window.uiManager = new UIManager();
