/**
 * Advanced Features Manager
 * Handles advanced UI features and interactions
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

class AdvancedFeatures {
    constructor(uiManager, dnaApp) {
        this.uiManager = uiManager;
        this.dnaApp = dnaApp;
        this.shortcuts = new Map();
        this.contextMenus = new Map();
        this.dragDropHandlers = new Map();
        this.animations = new Map();
        
        this.init();
    }
    
    init() {
        this.setupAdvancedShortcuts();
        this.setupDragAndDrop();
        this.setupContextMenus();
        this.setupAdvancedAnimations();
        this.setupProgressiveWebApp();
        this.setupAccessibility();
    }
    
    setupAdvancedShortcuts() {
        // Advanced keyboard shortcuts
        const shortcuts = {
            'ctrl+shift+e': () => this.quickEncrypt(),
            'ctrl+shift+d': () => this.quickDecrypt(),
            'ctrl+shift+c': () => this.copyToClipboard(),
            'ctrl+shift+v': () => this.pasteFromClipboard(),
            'ctrl+shift+s': () => this.saveCurrentState(),
            'ctrl+shift+l': () => this.loadSavedState(),
            'ctrl+shift+r': () => this.resetApplication(),
            'ctrl+shift+t': () => this.toggleTheme(),
            'ctrl+shift+f': () => this.toggleFullscreen(),
            'ctrl+shift+h': () => this.showHelp(),
            'f1': () => this.showKeyboardShortcuts(),
            'f5': () => this.refreshData(),
            'ctrl+z': () => this.undo(),
            'ctrl+y': () => this.redo()
        };
        
        Object.entries(shortcuts).forEach(([key, handler]) => {
            this.shortcuts.set(key, handler);
        });
        
        document.addEventListener('keydown', (e) => {
            const key = this.getKeyString(e);
            if (this.shortcuts.has(key)) {
                e.preventDefault();
                this.shortcuts.get(key)();
            }
        });
    }
    
    getKeyString(event) {
        const parts = [];
        if (event.ctrlKey) parts.push('ctrl');
        if (event.altKey) parts.push('alt');
        if (event.shiftKey) parts.push('shift');
        if (event.metaKey) parts.push('meta');
        
        const key = event.key.toLowerCase();
        if (!['control', 'alt', 'shift', 'meta'].includes(key)) {
            parts.push(key);
        }
        
        return parts.join('+');
    }
    
    setupDragAndDrop() {
        // Enhanced drag and drop functionality
        const dropZones = document.querySelectorAll('.file-drop-zone, .dna-input-modern');
        
        dropZones.forEach(zone => {
            this.setupDropZone(zone);
        });
    }
    
    setupDropZone(element) {
        const handlers = {
            dragenter: (e) => this.handleDragEnter(e, element),
            dragover: (e) => this.handleDragOver(e, element),
            dragleave: (e) => this.handleDragLeave(e, element),
            drop: (e) => this.handleDrop(e, element)
        };
        
        Object.entries(handlers).forEach(([event, handler]) => {
            element.addEventListener(event, handler);
        });
        
        this.dragDropHandlers.set(element, handlers);
    }
    
    handleDragEnter(e, element) {
        e.preventDefault();
        e.stopPropagation();
        element.classList.add('drag-over');
        
        // Add visual feedback
        if (typeof gsap !== 'undefined') {
            gsap.to(element, {
                duration: 0.2,
                scale: 1.02,
                boxShadow: '0 10px 30px rgba(99, 102, 241, 0.3)',
                ease: 'power2.out'
            });
        }
    }
    
    handleDragOver(e, element) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    handleDragLeave(e, element) {
        e.preventDefault();
        e.stopPropagation();
        
        // Only remove if actually leaving the element
        if (!element.contains(e.relatedTarget)) {
            element.classList.remove('drag-over');
            
            if (typeof gsap !== 'undefined') {
                gsap.to(element, {
                    duration: 0.2,
                    scale: 1,
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                    ease: 'power2.out'
                });
            }
        }
    }
    
    handleDrop(e, element) {
        e.preventDefault();
        e.stopPropagation();
        element.classList.remove('drag-over');
        
        // Reset visual state
        if (typeof gsap !== 'undefined') {
            gsap.to(element, {
                duration: 0.2,
                scale: 1,
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                ease: 'power2.out'
            });
        }
        
        const files = Array.from(e.dataTransfer.files);
        const text = e.dataTransfer.getData('text/plain');
        
        if (files.length > 0) {
            this.handleFilesDrop(files, element);
        } else if (text) {
            this.handleTextDrop(text, element);
        }
    }
    
    handleFilesDrop(files, element) {
        if (element.classList.contains('file-drop-zone')) {
            // Handle file upload
            const fileInput = document.getElementById('fileInput');
            if (fileInput && files.length > 0) {
                const dt = new DataTransfer();
                files.forEach(file => dt.items.add(file));
                fileInput.files = dt.files;
                fileInput.dispatchEvent(new Event('change'));
            }
        }
    }
    
    handleTextDrop(text, element) {
        if (element.classList.contains('dna-input-modern') || element.tagName === 'TEXTAREA') {
            const textarea = element.tagName === 'TEXTAREA' ? element : element.querySelector('textarea');
            if (textarea) {
                textarea.value = text;
                textarea.dispatchEvent(new Event('input'));
                this.uiManager.showNotification('متن با موفقیت چسبانده شد', 'success');
            }
        }
    }
    
    setupContextMenus() {
        // Enhanced context menus for different elements
        const contextMenus = {
            '.dna-sequence': this.createDNAContextMenu(),
            '.file-info-modern': this.createFileContextMenu(),
            '.result-section-modern': this.createResultContextMenu()
        };
        
        Object.entries(contextMenus).forEach(([selector, menu]) => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                this.attachContextMenu(element, menu);
            });
        });
    }
    
    createDNAContextMenu() {
        return [
            { label: 'کپی توالی', icon: 'fas fa-copy', action: 'copy-dna' },
            { label: 'فرمت کردن', icon: 'fas fa-align-left', action: 'format-dna' },
            { label: 'اعتبارسنجی', icon: 'fas fa-check-circle', action: 'validate-dna' },
            { label: 'تولید QR کد', icon: 'fas fa-qrcode', action: 'generate-qr' },
            { type: 'separator' },
            { label: 'ذخیره در فایل', icon: 'fas fa-save', action: 'save-dna' }
        ];
    }
    
    createFileContextMenu() {
        return [
            { label: 'مشاهده جزئیات', icon: 'fas fa-info-circle', action: 'view-details' },
            { label: 'کپی اطلاعات', icon: 'fas fa-copy', action: 'copy-info' },
            { label: 'حذف فایل', icon: 'fas fa-trash', action: 'remove-file' }
        ];
    }
    
    createResultContextMenu() {
        return [
            { label: 'کپی نتیجه', icon: 'fas fa-copy', action: 'copy-result' },
            { label: 'دانلود', icon: 'fas fa-download', action: 'download-result' },
            { label: 'اشتراک‌گذاری', icon: 'fas fa-share', action: 'share-result' },
            { type: 'separator' },
            { label: 'چاپ', icon: 'fas fa-print', action: 'print-result' }
        ];
    }
    
    attachContextMenu(element, menuItems) {
        element.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showContextMenu(e, menuItems, element);
        });
    }
    
    showContextMenu(event, menuItems, targetElement) {
        // Remove existing context menu
        const existingMenu = document.querySelector('.advanced-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }
        
        // Create new context menu
        const menu = document.createElement('div');
        menu.className = 'advanced-context-menu';
        menu.style.cssText = `
            position: fixed;
            left: ${event.pageX}px;
            top: ${event.pageY}px;
            z-index: 10000;
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-xl);
            padding: var(--spacing-xs);
            min-width: 180px;
        `;
        
        menuItems.forEach(item => {
            if (item.type === 'separator') {
                const separator = document.createElement('div');
                separator.className = 'context-menu-separator';
                separator.style.cssText = `
                    height: 1px;
                    background: var(--border-primary);
                    margin: var(--spacing-xs) 0;
                `;
                menu.appendChild(separator);
            } else {
                const menuItem = document.createElement('div');
                menuItem.className = 'context-menu-item';
                menuItem.style.cssText = `
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-sm);
                    padding: var(--spacing-sm) var(--spacing-md);
                    cursor: pointer;
                    border-radius: var(--radius-sm);
                    transition: all var(--transition-fast);
                    color: var(--text-secondary);
                    font-size: 0.9rem;
                `;
                
                menuItem.innerHTML = `
                    <i class="${item.icon}"></i>
                    <span>${item.label}</span>
                `;
                
                menuItem.addEventListener('mouseenter', () => {
                    menuItem.style.background = 'var(--bg-tertiary)';
                    menuItem.style.color = 'var(--text-primary)';
                });
                
                menuItem.addEventListener('mouseleave', () => {
                    menuItem.style.background = 'transparent';
                    menuItem.style.color = 'var(--text-secondary)';
                });
                
                menuItem.addEventListener('click', () => {
                    this.handleContextAction(item.action, targetElement);
                    menu.remove();
                });
                
                menu.appendChild(menuItem);
            }
        });
        
        document.body.appendChild(menu);
        
        // Remove menu when clicking outside
        const removeMenu = (e) => {
            if (!menu.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', removeMenu);
            }
        };
        
        setTimeout(() => {
            document.addEventListener('click', removeMenu);
        }, 0);
        
        // Animate in
        if (typeof gsap !== 'undefined') {
            gsap.from(menu, {
                duration: 0.2,
                scale: 0.9,
                opacity: 0,
                ease: 'power2.out'
            });
        }
    }
    
    handleContextAction(action, element) {
        switch (action) {
            case 'copy-dna':
                this.copyDNASequence(element);
                break;
            case 'format-dna':
                this.formatDNASequence(element);
                break;
            case 'validate-dna':
                this.validateDNASequence(element);
                break;
            case 'generate-qr':
                this.generateQRCode(element);
                break;
            case 'save-dna':
                this.saveDNAToFile(element);
                break;
            case 'view-details':
                this.viewFileDetails(element);
                break;
            case 'copy-info':
                this.copyFileInfo(element);
                break;
            case 'remove-file':
                this.removeFile(element);
                break;
            case 'copy-result':
                this.copyResult(element);
                break;
            case 'download-result':
                this.downloadResult(element);
                break;
            case 'share-result':
                this.shareResult(element);
                break;
            case 'print-result':
                this.printResult(element);
                break;
        }
    }
    
    // Context action implementations
    async copyDNASequence(element) {
        const sequence = element.textContent || element.value;
        try {
            await navigator.clipboard.writeText(sequence);
            this.uiManager.showNotification('توالی DNA کپی شد', 'success');
        } catch (err) {
            this.uiManager.showNotification('خطا در کپی کردن', 'error');
        }
    }
    
    formatDNASequence(element) {
        let sequence = element.textContent || element.value;
        sequence = sequence.toUpperCase().replace(/[^ATCG]/g, '');
        
        // Format in groups of 10 with line breaks every 50
        let formatted = '';
        for (let i = 0; i < sequence.length; i += 50) {
            const line = sequence.substr(i, 50);
            const groups = line.match(/.{1,10}/g) || [];
            formatted += groups.join(' ') + '\n';
        }
        
        if (element.tagName === 'TEXTAREA' || element.tagName === 'INPUT') {
            element.value = formatted.trim();
        } else {
            element.textContent = formatted.trim();
        }
        
        this.uiManager.showNotification('توالی DNA فرمت شد', 'success');
    }
    
    validateDNASequence(element) {
        const sequence = (element.textContent || element.value).toUpperCase();
        const validChars = /^[ATCG\s\n\r]*$/;
        const isValid = validChars.test(sequence) && sequence.replace(/\s/g, '').length > 0;
        
        this.uiManager.showNotification(
            isValid ? 'توالی DNA معتبر است' : 'توالی DNA نامعتبر',
            isValid ? 'success' : 'error'
        );
    }
    
    generateQRCode(element) {
        const sequence = element.textContent || element.value;
        if (typeof QRCode !== 'undefined') {
            // Implementation would use QRCode library
            this.uiManager.showNotification('کد QR تولید شد', 'success');
        } else {
            this.uiManager.showNotification('کتابخانه QR Code بارگذاری نشده', 'error');
        }
    }
    
    saveDNAToFile(element) {
        const sequence = element.textContent || element.value;
        const blob = new Blob([sequence], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `dna-sequence-${Date.now()}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.uiManager.showNotification('فایل DNA ذخیره شد', 'success');
    }
    
    // Quick action methods
    quickEncrypt() {
        this.uiManager.switchTab('encrypt');
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.click();
        }
    }
    
    quickDecrypt() {
        this.uiManager.switchTab('decrypt');
        const dnaInput = document.getElementById('dnaInput');
        if (dnaInput) {
            dnaInput.focus();
        }
    }
    
    async copyToClipboard() {
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === 'TEXTAREA' || activeElement.tagName === 'INPUT')) {
            try {
                await navigator.clipboard.writeText(activeElement.value);
                this.uiManager.showNotification('متن کپی شد', 'success');
            } catch (err) {
                this.uiManager.showNotification('خطا در کپی کردن', 'error');
            }
        }
    }
    
    async pasteFromClipboard() {
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === 'TEXTAREA' || activeElement.tagName === 'INPUT')) {
            try {
                const text = await navigator.clipboard.readText();
                activeElement.value = text;
                activeElement.dispatchEvent(new Event('input'));
                this.uiManager.showNotification('متن چسبانده شد', 'success');
            } catch (err) {
                this.uiManager.showNotification('خطا در چسباندن', 'error');
            }
        }
    }
    
    saveCurrentState() {
        const state = {
            currentTab: this.uiManager.currentTab,
            theme: this.uiManager.currentTheme,
            timestamp: Date.now()
        };
        
        localStorage.setItem('dna-app-saved-state', JSON.stringify(state));
        this.uiManager.showNotification('وضعیت ذخیره شد', 'success');
    }
    
    loadSavedState() {
        const savedState = localStorage.getItem('dna-app-saved-state');
        if (savedState) {
            try {
                const state = JSON.parse(savedState);
                this.uiManager.switchTab(state.currentTab);
                this.uiManager.setTheme(state.theme);
                this.uiManager.showNotification('وضعیت بازیابی شد', 'success');
            } catch (err) {
                this.uiManager.showNotification('خطا در بازیابی وضعیت', 'error');
            }
        } else {
            this.uiManager.showNotification('وضعیت ذخیره شده‌ای یافت نشد', 'warning');
        }
    }
    
    resetApplication() {
        if (confirm('آیا از بازنشانی برنامه اطمینان دارید؟')) {
            localStorage.clear();
            location.reload();
        }
    }
    
    toggleTheme() {
        this.uiManager.toggleTheme();
    }
    
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }
    
    showHelp() {
        this.uiManager.showNotification('راهنما به زودی اضافه خواهد شد', 'info');
    }
    
    showKeyboardShortcuts() {
        const modal = document.getElementById('shortcutsModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }
    
    refreshData() {
        this.dnaApp.loadStatistics();
        this.uiManager.showNotification('داده‌ها به‌روزرسانی شد', 'success');
    }
    
    undo() {
        // Implementation for undo functionality
        this.uiManager.showNotification('عملیات بازگردانی', 'info');
    }
    
    redo() {
        // Implementation for redo functionality
        this.uiManager.showNotification('عملیات تکرار', 'info');
    }
    
    setupAdvancedAnimations() {
        if (typeof gsap === 'undefined') return;
        
        // Setup scroll-triggered animations
        this.setupScrollAnimations();
        
        // Setup hover animations
        this.setupHoverAnimations();
        
        // Setup loading animations
        this.setupLoadingAnimations();
    }
    
    setupScrollAnimations() {
        // Animate elements as they come into view
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateElementIn(entry.target);
                }
            });
        }, observerOptions);
        
        // Observe dashboard cards and other elements
        const elementsToObserve = document.querySelectorAll(
            '.dashboard-card, .encrypt-card, .decrypt-card, .settings-card'
        );
        
        elementsToObserve.forEach(el => observer.observe(el));
    }
    
    animateElementIn(element) {
        if (typeof gsap !== 'undefined' && this.uiManager.animationEnabled) {
            gsap.from(element, {
                duration: 0.6,
                y: 30,
                opacity: 0,
                ease: 'power2.out'
            });
        }
    }
    
    setupHoverAnimations() {
        // Enhanced hover effects for interactive elements
        const interactiveElements = document.querySelectorAll(
            '.btn-modern, .dashboard-card, .nav-link, .tool-btn-modern'
        );
        
        interactiveElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                if (typeof gsap !== 'undefined' && this.uiManager.animationEnabled) {
                    gsap.to(element, {
                        duration: 0.2,
                        scale: 1.02,
                        ease: 'power2.out'
                    });
                }
            });
            
            element.addEventListener('mouseleave', () => {
                if (typeof gsap !== 'undefined' && this.uiManager.animationEnabled) {
                    gsap.to(element, {
                        duration: 0.2,
                        scale: 1,
                        ease: 'power2.out'
                    });
                }
            });
        });
    }
    
    setupLoadingAnimations() {
        // Custom loading animations for different states
        this.animations.set('pulse', (element) => {
            if (typeof gsap !== 'undefined') {
                gsap.to(element, {
                    duration: 1,
                    scale: 1.05,
                    opacity: 0.7,
                    yoyo: true,
                    repeat: -1,
                    ease: 'power2.inOut'
                });
            }
        });
        
        this.animations.set('rotate', (element) => {
            if (typeof gsap !== 'undefined') {
                gsap.to(element, {
                    duration: 2,
                    rotation: 360,
                    repeat: -1,
                    ease: 'none'
                });
            }
        });
    }
    
    setupProgressiveWebApp() {
        // PWA functionality
        if ('serviceWorker' in navigator) {
            this.registerServiceWorker();
        }
        
        // Install prompt
        this.setupInstallPrompt();
        
        // Offline detection
        this.setupOfflineDetection();
    }
    
    registerServiceWorker() {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('Service Worker registered:', registration);
            })
            .catch(error => {
                console.log('Service Worker registration failed:', error);
            });
    }
    
    setupInstallPrompt() {
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // Show install button
            const installBtn = document.createElement('button');
            installBtn.textContent = 'نصب برنامه';
            installBtn.className = 'btn-modern btn-primary';
            installBtn.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1000;
            `;
            
            installBtn.addEventListener('click', () => {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        this.uiManager.showNotification('برنامه نصب شد', 'success');
                    }
                    deferredPrompt = null;
                    installBtn.remove();
                });
            });
            
            document.body.appendChild(installBtn);
        });
    }
    
    setupOfflineDetection() {
        window.addEventListener('online', () => {
            this.uiManager.showNotification('اتصال اینترنت برقرار شد', 'success');
        });
        
        window.addEventListener('offline', () => {
            this.uiManager.showNotification('اتصال اینترنت قطع شد', 'warning');
        });
    }
    
    setupAccessibility() {
        // Enhanced accessibility features
        this.setupKeyboardNavigation();
        this.setupScreenReaderSupport();
        this.setupHighContrastMode();
    }
    
    setupKeyboardNavigation() {
        // Tab navigation enhancement
        const focusableElements = document.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        focusableElements.forEach((element, index) => {
            element.addEventListener('keydown', (e) => {
                if (e.key === 'Tab') {
                    // Custom tab navigation logic if needed
                }
            });
        });
    }
    
    setupScreenReaderSupport() {
        // Add ARIA labels and descriptions
        const elements = document.querySelectorAll('[data-aria-label]');
        elements.forEach(element => {
            element.setAttribute('aria-label', element.dataset.ariaLabel);
        });
        
        // Live regions for dynamic content
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(liveRegion);
        
        // Update live region when notifications are shown
        const originalShowNotification = this.uiManager.showNotification;
        this.uiManager.showNotification = (message, type, title) => {
            liveRegion.textContent = `${title || ''} ${message}`;
            return originalShowNotification.call(this.uiManager, message, type, title);
        };
    }
    
    setupHighContrastMode() {
        // Detect high contrast preference
        if (window.matchMedia('(prefers-contrast: high)').matches) {
            document.body.classList.add('high-contrast');
        }
        
        // Listen for changes
        window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
            document.body.classList.toggle('high-contrast', e.matches);
        });
    }
}

// Export for use in main application
window.AdvancedFeatures = AdvancedFeatures;
