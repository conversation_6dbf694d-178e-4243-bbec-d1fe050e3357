/**
 * DNA Encryption Platform
 * Advanced file encryption using DNA sequence encoding
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @license MIT
 */

class DNAEncryptionApp {
    constructor() {
        this.dnaCodec = new DNACodec();
        this.cryptoEngine = new AdvancedCryptoEngine();
        this.fileHandler = new AdvancedFileHandler();
        this.storageManager = new StorageManager();
        this.uiManager = new UIManager();
        
        this.currentFile = null;
        this.isProcessing = false;
        
        // Developer info
        this.developer = 'Mezd';
        this.version = '2.0.0';
        
        // پیام‌های فارسی
        this.messages = {
            initSuccess: 'پلتفرم رمزگذاری DNA با موفقیت راه‌اندازی شد! - توسط Mezd',
            initError: 'خطا در راه‌اندازی برنامه: ',
            fileSelectSuccess: 'فایل "{fileName}" با موفقیت انتخاب شد',
            noFileSelected: 'لطفاً ابتدا یک فایل انتخاب کنید',
            noPassword: 'لطفاً رمز عبور را وارد کنید',
            noDnaSequence: 'لطفاً توالی DNA را وارد کنید',
            processingFile: 'در حال پردازش فایل...',
            encrypting: 'در حال رمزگذاری داده‌ها...',
            encodingDNA: 'در حال تبدیل به DNA...',
            encryptionSuccess: 'فایل با موفقیت به DNA رمزگذاری شد!',
            encryptionError: 'خطا در رمزگذاری: ',
            decodingDNA: 'در حال تبدیل از DNA...',
            decrypting: 'در حال رمزگشایی داده‌ها...',
            decryptionError: 'خطا در رمزگشایی: ',
            weakPassword: 'رمز عبور وارد شده ضعیف است. لطفاً یک رمز عبور قوی‌تر انتخاب کنید.'
        };
        
        // console.log(`🧬 DNA Encryption Platform v${this.version} - Developed by ${this.developer}`);
    }

    async initialize() {
        try {
            // Initialize storage
            await this.storageManager.initialize();

            // Initialize UI with loading screen
            await this.uiManager.initialize();

            // Setup event listeners
            this.setupEventListeners();

            // Load statistics
            await this.loadStatistics();

            // Setup password strength checker
            this.setupPasswordStrength();

            // Setup file validation
            this.setupFileValidation();

            // Initialize dashboard
            this.initializeDashboard();

            this.uiManager.showNotification(this.messages.initSuccess, 'success', 'سیستم آماده');
        } catch (error) {
            this.uiManager.showNotification(this.messages.initError + error.message, 'error', 'خطای راه‌اندازی');
        }
    }

    setupEventListeners() {
        // File input handler
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelect(e.target.files[0]);
            });
        }

        // Encryption button
        const encryptBtn = document.getElementById('encryptBtn');
        if (encryptBtn) {
            encryptBtn.addEventListener('click', () => {
                this.handleEncryption();
            });
        }

        // Decryption button
        const decryptBtn = document.getElementById('decryptBtn');
        if (decryptBtn) {
            decryptBtn.addEventListener('click', () => {
                this.handleDecryption();
            });
        }

        // Password strength indicator
        const encryptPassword = document.getElementById('encryptPassword');
        if (encryptPassword) {
            encryptPassword.addEventListener('input', (e) => {
                this.updatePasswordStrength(e.target.value);
            });
        }
    }

    handleFileSelect(file) {
        if (!file) return;

        try {
            this.fileHandler.validateFile(file);
            this.currentFile = file;
            
            const metadata = this.fileHandler.getFileMetadata(file);
            this.displayFileInfo(metadata);
            
            this.uiManager.showSuccess(this.messages.fileSelectSuccess.replace('{fileName}', file.name));
        } catch (error) {
            this.uiManager.showError(error.message);
        }
    }

    async handleEncryption() {
        if (!this.currentFile) {
            this.uiManager.showError(this.messages.noFileSelected);
            return;
        }

        const password = document.getElementById('encryptPassword').value;
        if (!password) {
            this.uiManager.showError(this.messages.noPassword);
            return;
        }
        // Check password strength
        const strength = this.calculatePasswordStrength(password);
        if (strength.level === 'weak') {
            this.uiManager.showError(this.messages.weakPassword);
            return;
        }

        if (this.isProcessing) return;
        this.isProcessing = true;

        try {
            this.uiManager.showProgress(this.messages.processingFile, 0);

            // Process file in chunks
            const fileData = await this.fileHandler.processLargeFile(
                this.currentFile,
                (progress) => this.uiManager.updateProgress(progress * 0.3)
            );

            this.uiManager.showProgress(this.messages.encrypting, 30);

            // Encrypt data
            const encryptedData = await this.cryptoEngine.encryptBeforeDNA(fileData, password);
            this.uiManager.updateProgress(60);

            // Encode to DNA
            const dnaSequence = this.dnaCodec.encodeWithErrorCorrection(encryptedData);
            this.uiManager.updateProgress(90);

            // Store encryption record
            const encryptionId = await this.storageManager.storeEncryption({
                fileName: this.currentFile.name,
                fileSize: this.currentFile.size,
                timestamp: Date.now(),
                dnaLength: dnaSequence.length
            });

            this.uiManager.updateProgress(100);
            this.uiManager.hideProgress();

            // Display results
            this.uiManager.displayEncryptionResult(dnaSequence, encryptionId);
            this.uiManager.showSuccess(this.messages.encryptionSuccess);

        } catch (error) {
            this.uiManager.hideProgress();
            this.uiManager.showError(this.messages.encryptionError + error.message);
        } finally {
            this.isProcessing = false;
        }
    }

    async handleDecryption() {
        const dnaSequence = document.getElementById('dnaInput').value.trim();
        const password = document.getElementById('decryptPassword').value;

        if (!dnaSequence) {
            this.uiManager.showError(this.messages.noDnaSequence);
            return;
        }

        if (!password) {
            this.uiManager.showError(this.messages.noPassword);
            return;
        }

        if (this.isProcessing) return;
        this.isProcessing = true;

        try {
            this.uiManager.showProgress(this.messages.decodingDNA, 0);

            // Decode from DNA
            const encryptedData = this.dnaCodec.decodeWithErrorCorrection(dnaSequence);
            this.uiManager.updateProgress(30);

            this.uiManager.showProgress(this.messages.decrypting, 30);

            // Decrypt data
            const decryptedData = await this.cryptoEngine.decryptAfterDNA(encryptedData, password);
            this.uiManager.updateProgress(80);

            // Detect file type and create filename
            const fileType = this.fileHandler.detectFileType(decryptedData);
            const extension = this.getFileExtension(fileType);
            const filename = `decrypted_${Date.now()}${extension}`;

            this.uiManager.updateProgress(100);
            this.uiManager.hideProgress();

            // Offer download
            this.uiManager.offerDownload(decryptedData, filename);

        } catch (error) {
            this.uiManager.hideProgress();
            this.uiManager.showError(this.messages.decryptionError + error.message);
        } finally {
            this.isProcessing = false;
        }
    }

    displayFileInfo(metadata) {
        const fileInfo = document.getElementById('fileInfo');
        if (fileInfo) {
            fileInfo.innerHTML = `
                <div class="file-info-card">
                    <h4>📄 File Information</h4>
                    <p><strong>Name:</strong> ${metadata.name}</p>
                    <p><strong>Size:</strong> ${this.formatFileSize(metadata.size)}</p>
                    <p><strong>Type:</strong> ${metadata.type}</p>
                    <p><strong>Modified:</strong> ${metadata.lastModifiedDate.toLocaleString()}</p>
                </div>
            `;
        }
    }

    updatePasswordStrength(password) {
        const strength = this.calculatePasswordStrength(password);
        const strengthBar = document.querySelector('.strength-fill');
        const strengthText = document.querySelector('.strength-text');

        if (strengthBar) {
            strengthBar.style.width = `${strength.percentage}%`;
            strengthBar.className = `strength-fill ${strength.level}`;
        }

        if (strengthText) {
            strengthText.textContent = strength.text;
        }

        // Update requirements
        this.updatePasswordRequirements(password);
    }

    updatePasswordRequirements(password) {
        const requirements = [
            { id: 'req-length', test: password.length >= 8 },
            { id: 'req-upper', test: /[A-Z]/.test(password) },
            { id: 'req-lower', test: /[a-z]/.test(password) },
            { id: 'req-number', test: /[0-9]/.test(password) },
            { id: 'req-special', test: /[^A-Za-z0-9]/.test(password) }
        ];

        requirements.forEach(req => {
            const element = document.getElementById(req.id);
            if (element) {
                const icon = element.querySelector('i');
                if (req.test) {
                    element.classList.add('valid');
                    element.classList.remove('invalid');
                    if (icon) icon.className = 'fas fa-check';
                } else {
                    element.classList.add('invalid');
                    element.classList.remove('valid');
                    if (icon) icon.className = 'fas fa-times';
                }
            }
        });
    }

    calculatePasswordStrength(password) {
        let score = 0;

        if (password.length >= 8) score++;
        if (password.length >= 12) score++;
        if (/[a-z]/.test(password)) score++;
        if (/[A-Z]/.test(password)) score++;
        if (/[0-9]/.test(password)) score++;
        if (/[^A-Za-z0-9]/.test(password)) score++;

        const levels = [
            { level: 'weak', text: 'ضعیف', percentage: 20 },
            { level: 'weak', text: 'ضعیف', percentage: 30 },
            { level: 'medium', text: 'متوسط', percentage: 50 },
            { level: 'medium', text: 'خوب', percentage: 70 },
            { level: 'strong', text: 'قوی', percentage: 90 },
            { level: 'strong', text: 'بسیار قوی', percentage: 100 }
        ];

        return levels[Math.min(score, 5)];
    }

    formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    getFileExtension(mimeType) {
        const extensions = {
            'image/jpeg': '.jpg',
            'image/png': '.png',
            'application/pdf': '.pdf',
            'text/plain': '.txt',
            'application/zip': '.zip',
            'audio/mp3': '.mp3',
            'video/mp4': '.mp4'
        };
        if (!mimeType || !extensions[mimeType]) {
            return '.bin';
        }
        return extensions[mimeType];
    }

    // New methods for enhanced functionality
    setupPasswordStrength() {
        const encryptPassword = document.getElementById('encryptPassword');
        if (encryptPassword) {
            encryptPassword.addEventListener('input', (e) => {
                this.updatePasswordStrength(e.target.value);
            });
        }
    }

    setupFileValidation() {
        // Load saved settings
        const savedMaxSize = localStorage.getItem('dna-app-max-file-size');
        if (savedMaxSize) {
            this.fileHandler.maxFileSize = parseInt(savedMaxSize) * 1024 * 1024;
        }

        const savedCompression = localStorage.getItem('dna-app-compression-level');
        if (savedCompression) {
            this.dnaCodec.compressionLevel = parseInt(savedCompression);
        }
    }

    async loadStatistics() {
        try {
            const encryptions = await this.storageManager.getAllEncryptions();

            // Update dashboard stats
            const totalEncryptions = document.getElementById('totalEncryptions');
            const totalFiles = document.getElementById('totalFiles');
            const totalSize = document.getElementById('totalSize');

            if (totalEncryptions) {
                totalEncryptions.textContent = encryptions.length.toLocaleString('fa-IR');
            }

            if (totalFiles) {
                totalFiles.textContent = encryptions.length.toLocaleString('fa-IR');
            }

            if (totalSize) {
                const size = encryptions.reduce((sum, enc) => sum + (enc.fileSize || 0), 0);
                totalSize.textContent = this.formatFileSize(size);
            }

            // Update charts
            this.updateDashboardCharts(encryptions);

        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    initializeDashboard() {
        // Load saved preferences
        const savedAnimations = localStorage.getItem('dna-app-animations');
        if (savedAnimations !== null) {
            this.uiManager.animationEnabled = savedAnimations === 'true';
            const toggle = document.getElementById('enableAnimations');
            if (toggle) toggle.checked = this.uiManager.animationEnabled;
        }

        const savedSounds = localStorage.getItem('dna-app-sounds');
        if (savedSounds !== null) {
            this.uiManager.soundEnabled = savedSounds === 'true';
            const toggle = document.getElementById('enableSounds');
            if (toggle) toggle.checked = this.uiManager.soundEnabled;
        }

        // Update recent activity
        this.updateRecentActivity();
    }

    updateRecentActivity() {
        const activityContainer = document.getElementById('recentActivity');
        if (!activityContainer) return;

        // Add system ready activity
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';
        activityItem.innerHTML = `
            <div class="activity-icon success">
                <i class="fas fa-check"></i>
            </div>
            <div class="activity-content">
                <div class="activity-title">سیستم آماده است</div>
                <div class="activity-time">همین الان</div>
            </div>
        `;

        activityContainer.appendChild(activityItem);
    }

    updateDashboardCharts(encryptions) {
        if (!this.uiManager.charts || !this.uiManager.charts.activity) return;

        // Update activity chart with sample data
        const data = new Array(7).fill(0);

        // Add some sample activity
        data[new Date().getDay()] = encryptions.length;

        this.uiManager.charts.activity.data.datasets[0].data = data;
        this.uiManager.charts.activity.update();
    }

    formatFileSize(bytes) {
        const sizes = ['بایت', 'کیلوبایت', 'مگابایت', 'گیگابایت'];
        if (bytes === 0) return '0 بایت';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        const size = Math.round(bytes / Math.pow(1024, i) * 100) / 100;
        return `${size.toLocaleString('fa-IR')} ${sizes[i]}`;
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Check for required classes
        const requiredClasses = ['DNACodec', 'AdvancedCryptoEngine', 'AdvancedFileHandler', 'StorageManager', 'UIManager'];
        const missingClasses = requiredClasses.filter(className => typeof window[className] === 'undefined');

        if (missingClasses.length > 0) {
            console.error('Missing required classes:', missingClasses);
            alert('یکی از ماژول‌های اصلی برنامه بارگذاری نشده است. لطفاً صفحه را مجدداً بارگذاری کنید.');
            return;
        }

        // Initialize the main application
        window.dnaApp = new DNAEncryptionApp();
        await window.dnaApp.initialize();

        console.log('🧬 DNA Encryption Platform loaded successfully!');

    } catch (error) {
        console.error('Failed to initialize DNA Encryption App:', error);
        alert('خطا در راه‌اندازی برنامه. لطفاً صفحه را مجدداً بارگذاری کنید.');
    }
});

