#!/usr/bin/env python3
"""
Simple HTTP server for testing the DNA Encryption Platform
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # Set proper MIME types
        if self.path.endswith('.js'):
            self.send_header('Content-Type', 'application/javascript')
        elif self.path.endswith('.css'):
            self.send_header('Content-Type', 'text/css')
        elif self.path.endswith('.html'):
            self.send_header('Content-Type', 'text/html; charset=utf-8')
        
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def main():
    port = 8000
    
    # Change to the directory containing this script
    os.chdir(Path(__file__).parent)
    
    print(f"🧬 DNA Encryption Platform Server")
    print(f"Starting server on port {port}...")
    print(f"Open your browser and go to: http://localhost:{port}")
    print(f"Test page: http://localhost:{port}/test.html")
    print(f"Main app: http://localhost:{port}/index.html")
    print("Press Ctrl+C to stop the server")
    
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
        sys.exit(0)
    except OSError as e:
        if e.errno == 10048:  # Port already in use on Windows
            print(f"Port {port} is already in use. Trying port {port + 1}...")
            port += 1
            with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
                print(f"Server started on port {port}")
                httpd.serve_forever()
        else:
            raise

if __name__ == "__main__":
    main()
